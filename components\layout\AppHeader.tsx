"use client"

import { Grid3X3, ShoppingBag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  return (
    <header className="sticky top-0 z-50 flex items-center justify-between p-4 lg:p-6 bg-slate-800/80 backdrop-blur-xl rounded-b-3xl mx-4 lg:mx-8 mt-4 lg:mt-6 border border-slate-700/50 shadow-2xl">
      <Button
        variant="ghost"
        size="icon"
        className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
        onClick={onMenuOpen}
      >
        <Grid3X3 className="h-6 w-6" />
      </Button>

      <div className="text-center">
        <h1 className="text-xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
          رايه شوب
        </h1>
      </div>

      <Button
        variant="ghost"
        size="icon"
        className="text-white bg-gradient-to-r from-pink-500 to-rose-500 rounded-full hover:scale-110 transition-all duration-300 shadow-lg"
      >
        <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
      </Button>
    </header>
  )
}
