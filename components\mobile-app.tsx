"use client"

import { useState, useEffect } from "react"
import {
  Grid3X3,
  ShoppingBag,
  User,
  CreditCard,
  HelpCircle,
  Settings,
  X,
  Home,
  Store,
  Gift,
  Phone,
  Info,
  ChevronRight,
  ChevronLeft,
} from "lucide-react"
import { Button } from "@/components/ui/button"

export default function MobileApp() {
  const [activeTab, setActiveTab] = useState("home")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  const gameCards = [
    {
      title: "FREE FIRE",
      subtitle: "شحن جواهر",
      gradient: "from-blue-500 via-blue-600 to-purple-600",
      icon: "🔥",
    },
    {
      title: "شحن عبر الـ ID",
      subtitle: "شحن فوري",
      gradient: "from-orange-500 via-red-500 to-red-600",
      icon: "⚡",
    },
    {
      title: "شحن جواهر بدوم",
      subtitle: "FREE FIRE",
      gradient: "from-green-500 via-emerald-500 to-blue-600",
      icon: "💎",
    },
    {
      title: "شحن جواهر فوري",
      subtitle: "FREE FIRE",
      gradient: "from-purple-500 via-pink-500 to-pink-600",
      icon: "🎮",
    },
    {
      title: "PUBG Mobile",
      subtitle: "شحن UC",
      gradient: "from-yellow-500 via-orange-500 to-red-600",
      icon: "🎯",
    },
    {
      title: "Call of Duty",
      subtitle: "شحن CP",
      gradient: "from-gray-600 via-gray-700 to-black",
      icon: "🔫",
    },
  ]

  const menuItems = [
    { icon: <Home className="h-5 w-5" />, label: "الرئيسية", href: "#" },
    { icon: <Store className="h-5 w-5" />, label: "المتجر", href: "#" },
    { icon: <Gift className="h-5 w-5" />, label: "العروض", href: "#" },
    { icon: <User className="h-5 w-5" />, label: "حسابي", href: "#" },
    { icon: <Phone className="h-5 w-5" />, label: "اتصل بنا", href: "#" },
    { icon: <Info className="h-5 w-5" />, label: "من نحن", href: "#" },
  ]

  // Slider data
  const slides = [
    {
      id: 1,
      title: "يمكنك الآن شحن جميع الألعاب",
      subtitle: "من خلال تطبيق واحد فقط",
      buttonText: "ابدأ اللعب الآن",
      gradient: "from-pink-100 via-yellow-200 to-yellow-400",
      image: "/placeholder.svg?height=300&width=400",
    },
    {
      id: 2,
      title: "أسرع خدمة شحن في السودان",
      subtitle: "شحن فوري خلال دقائق معدودة",
      buttonText: "اشحن الآن",
      gradient: "from-blue-100 via-purple-200 to-purple-400",
      image: "/placeholder.svg?height=300&width=400",
    },
    {
      id: 3,
      title: "عروض حصرية وخصومات مميزة",
      subtitle: "وفر أكثر مع عروضنا الخاصة",
      buttonText: "اكتشف العروض",
      gradient: "from-green-100 via-emerald-200 to-emerald-400",
      image: "/placeholder.svg?height=300&width=400",
    },
  ]

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [slides.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-yellow-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <header className="sticky top-0 z-50 flex items-center justify-between p-4 lg:p-6 bg-slate-800/80 backdrop-blur-xl rounded-b-3xl mx-4 lg:mx-8 mt-4 lg:mt-6 border border-slate-700/50 shadow-2xl">
        <Button
          variant="ghost"
          size="icon"
          className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
          onClick={() => setIsMenuOpen(true)}
        >
          <Grid3X3 className="h-6 w-6" />
        </Button>

        <div className="text-center">
          <h1 className="text-xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            رايه شوب
          </h1>
        </div>

        <Button
          variant="ghost"
          size="icon"
          className="text-white bg-gradient-to-r from-pink-500 to-rose-500 rounded-full hover:scale-110 transition-all duration-300 shadow-lg"
        >
          <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
        </Button>
      </header>

      {/* News Ticker */}
      <div className="sticky top-20 lg:top-24 z-40 bg-slate-900/90 backdrop-blur-xl border-y border-slate-700/50 overflow-hidden shadow-lg">
        <div className="ticker-wrapper">
          <div className="ticker-content">
            🔥 شحن سريع وآمن لجميع الألعاب الإلكترونية في السودان | ⚡ أفضل وأرخص موقع لشحن الألعاب في السودان | 🚀 وفر
            وقتك ومالك معنا | 💎 خدمة عملاء 24/7 | 🎮 أكثر من 50 ألف عميل راضٍ | 🔥 شحن سريع وآمن لجميع الألعاب
            الإلكترونية في السودان | ⚡ أفضل وأرخص موقع لشحن الألعاب في السودان | 🚀 وفر وقتك ومالك معنا
          </div>
        </div>
      </div>

      {/* Side Menu Overlay */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* Side Menu */}
      <div
        className={`fixed top-0 right-0 h-full w-80 lg:w-96 bg-white/10 backdrop-blur-2xl z-50 transform transition-transform duration-300 ease-out border-l border-white/20 shadow-2xl ${
          isMenuOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="p-6">
          {/* Menu Header */}
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              رايه شوب
            </h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(false)}
              className="text-white hover:bg-white/10 rounded-full"
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Menu Items */}
          <nav className="space-y-2">
            {menuItems.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="flex items-center justify-between p-4 rounded-xl hover:bg-white/20 backdrop-blur-sm transition-all duration-300 group border border-transparent hover:border-white/30"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="flex items-center gap-4">
                  <div className="text-yellow-400 group-hover:scale-110 transition-transform duration-300">
                    {item.icon}
                  </div>
                  <span className="font-medium text-lg">{item.label}</span>
                </div>
                <ChevronRight className="h-4 w-4 text-slate-400 group-hover:text-white transition-colors duration-300" />
              </a>
            ))}
          </nav>

          {/* Menu Footer */}
          <div className="absolute bottom-6 left-6 right-6">
            <div className="bg-gradient-to-r from-yellow-400/30 to-orange-500/30 backdrop-blur-sm rounded-xl p-4 border border-yellow-400/40 shadow-lg">
              <p className="text-sm text-center text-yellow-400 font-medium">🎮 أفضل متجر للألعاب الرقمية</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-8 pb-32 pt-8 max-w-7xl mx-auto">
        {/* Promotional Banner Slider */}
        <section className="relative rounded-3xl overflow-hidden shadow-2xl">
          <div className="relative h-64 lg:h-80">
            {slides.map((slide, index) => (
              <div
                key={slide.id}
                className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
                  index === currentSlide
                    ? "translate-x-0"
                    : index < currentSlide
                      ? "-translate-x-full"
                      : "translate-x-full"
                }`}
              >
                <div className={`h-full bg-gradient-to-r ${slide.gradient} p-6 lg:p-8`}>
                  <div className="flex flex-col lg:flex-row items-center gap-6 lg:gap-8 h-full">
                    {/* Game Thumbnails */}
                    <div className="grid grid-cols-3 gap-3 lg:gap-4 flex-1 lg:max-w-xs">
                      {Array.from({ length: 6 }).map((_, i) => (
                        <div
                          key={i}
                          className="w-16 h-16 lg:w-20 lg:h-20 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center text-slate-600 text-lg lg:text-xl font-bold shadow-lg hover:scale-110 transition-transform duration-300 cursor-pointer"
                        >
                          🎮
                        </div>
                      ))}
                    </div>

                    {/* CTA Content */}
                    <div className="flex-1 text-center lg:text-right">
                      <h2 className="text-slate-900 font-bold text-2xl lg:text-4xl mb-4 leading-tight">
                        {slide.title}
                      </h2>
                      <p className="text-slate-700 text-lg lg:text-xl mb-6">{slide.subtitle} ⚡</p>
                      <Button className="bg-slate-900 hover:bg-slate-800 text-white text-lg lg:text-xl px-8 lg:px-12 py-4 lg:py-6 rounded-full font-semibold shadow-lg hover:scale-105 transition-all duration-300">
                        {slide.buttonText}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Slider Controls */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 lg:p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
          >
            <ChevronLeft className="h-5 w-5 lg:h-6 lg:w-6" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 lg:p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
          >
            <ChevronRight className="h-5 w-5 lg:h-6 lg:w-6" />
          </button>

          {/* Slider Indicators */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide ? "bg-white shadow-lg" : "bg-white/50 hover:bg-white/70"
                }`}
              />
            ))}
          </div>
        </section>

        {/* Section Title */}
        <section className="text-center space-y-6">
          <h3 className="text-yellow-400 font-bold text-xl lg:text-3xl leading-relaxed">
            ⚡ أفضل وأرخص موقع لشحن الألعاب في السوق
          </h3>
          <div className="w-full h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
        </section>

        {/* Game Cards Grid */}
        <section className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
          {gameCards.map((card, idx) => (
            <div
              key={idx}
              className={`relative rounded-3xl overflow-hidden h-40 lg:h-48 bg-gradient-to-br ${card.gradient} shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer group`}
            >
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300" />
              <div className="relative h-full p-5 lg:p-6 flex flex-col justify-between">
                <div className="flex justify-center">
                  <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-3 lg:p-4 group-hover:scale-110 transition-transform duration-300">
                    <div className="text-2xl lg:text-3xl">{card.icon}</div>
                  </div>
                </div>
                <div className="text-center">
                  <h4 className="text-white font-bold text-sm lg:text-base mb-1 drop-shadow-lg">{card.title}</h4>
                  <p className="text-white/90 text-xs lg:text-sm drop-shadow-md">{card.subtitle}</p>
                </div>
              </div>
              {/* Shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
            </div>
          ))}
        </section>

        {/* Additional Content for Desktop */}
        <section className="hidden lg:block">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-12">
            {/* Features Section */}
            <div className="bg-slate-800/50 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50">
              <h3 className="text-2xl font-bold text-yellow-400 mb-6">لماذا تختار رايه شوب؟</h3>
              <div className="space-y-4">
                {[
                  { icon: "⚡", title: "شحن فوري", desc: "احصل على منتجك خلال دقائق" },
                  { icon: "💰", title: "أسعار منافسة", desc: "أفضل الأسعار في السوق" },
                  { icon: "🔒", title: "دفع آمن", desc: "معاملات محمية 100%" },
                  { icon: "🎧", title: "دعم 24/7", desc: "فريق دعم متاح دائماً" },
                ].map((feature, idx) => (
                  <div key={idx} className="flex items-center gap-4 p-4 rounded-xl hover:bg-white/5 transition-colors">
                    <div className="text-2xl">{feature.icon}</div>
                    <div>
                      <h4 className="font-semibold text-white">{feature.title}</h4>
                      <p className="text-slate-400 text-sm">{feature.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Stats Section */}
            <div className="bg-gradient-to-br from-yellow-400/10 to-orange-500/10 backdrop-blur-xl rounded-3xl p-8 border border-yellow-400/20">
              <h3 className="text-2xl font-bold text-yellow-400 mb-6">إحصائياتنا</h3>
              <div className="grid grid-cols-2 gap-6">
                {[
                  { number: "50K+", label: "عميل راضٍ" },
                  { number: "100K+", label: "عملية شحن" },
                  { number: "24/7", label: "دعم فني" },
                  { number: "99%", label: "نسبة النجاح" },
                ].map((stat, idx) => (
                  <div key={idx} className="text-center">
                    <div className="text-3xl font-bold text-yellow-400 mb-2">{stat.number}</div>
                    <div className="text-slate-300 text-sm">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Floating Bottom Navigation - Mobile Only */}
      <nav className="lg:hidden fixed bottom-6 left-1/2 w-[calc(100%-2rem)] max-w-sm -translate-x-1/2 z-40">
        <div className="bg-slate-800/80 backdrop-blur-2xl rounded-3xl px-6 py-4 shadow-2xl border border-slate-700/50">
          <div className="flex items-center justify-around">
            {[
              { id: "account", icon: <User className="h-5 w-5" />, label: "حسابي" },
              { id: "payment", icon: <CreditCard className="h-5 w-5" />, label: "تغذية" },
              {
                id: "home",
                icon: <Home className="h-6 w-6" />,
                label: "",
                center: true,
              },
              { id: "requests", icon: <HelpCircle className="h-5 w-5" />, label: "طلباتي" },
              { id: "support", icon: <Settings className="h-5 w-5" />, label: "الدعم" },
            ].map(({ id, icon, label, center }) =>
              center ? (
                <button
                  key={id}
                  onClick={() => setActiveTab(id)}
                  className="flex flex-col items-center p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg hover:scale-110 transition-all duration-300 text-slate-900"
                >
                  {icon}
                </button>
              ) : (
                <button
                  key={id}
                  onClick={() => setActiveTab(id)}
                  className={`flex flex-col items-center space-y-1 p-3 rounded-2xl transition-all duration-300 hover:scale-110 ${
                    activeTab === id
                      ? "bg-white/20 text-yellow-400 shadow-lg"
                      : "text-slate-400 hover:text-white hover:bg-white/10"
                  }`}
                >
                  {icon}
                  <span className="text-xs font-medium">{label}</span>
                </button>
              ),
            )}
          </div>
        </div>
      </nav>

      {/* Desktop Footer Navigation */}
      <footer className="hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12">
        <div className="max-w-7xl mx-auto px-8 py-8">
          <div className="flex items-center justify-center gap-8">
            {[
              { id: "account", icon: <User className="h-6 w-6" />, label: "حسابي" },
              { id: "payment", icon: <CreditCard className="h-6 w-6" />, label: "تغذية" },
              { id: "home", icon: <Home className="h-6 w-6" />, label: "الرئيسية" },
              { id: "requests", icon: <HelpCircle className="h-6 w-6" />, label: "طلباتي" },
              { id: "support", icon: <Settings className="h-6 w-6" />, label: "الدعم الفني" },
            ].map(({ id, icon, label }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 hover:scale-105 ${
                  activeTab === id
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg"
                    : "text-slate-400 hover:text-white hover:bg-white/10"
                }`}
              >
                {icon}
                <span className="text-sm font-medium">{label}</span>
              </button>
            ))}
          </div>
        </div>
      </footer>
    </div>
  )
}
