import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function FeaturedDeals() {
  const deals = [
    {
      title: "شحن ببجي UC",
      originalPrice: "50",
      discountPrice: "35",
      discount: "30%",
      image: "/placeholder.svg?height=200&width=300",
      badge: "الأكثر مبيعاً",
    },
    {
      title: "بطاقة آيتونز 100$",
      originalPrice: "120",
      discountPrice: "100",
      discount: "17%",
      image: "/placeholder.svg?height=200&width=300",
      badge: "عرض محدود",
    },
    {
      title: "شحن فري فاير",
      originalPrice: "30",
      discountPrice: "20",
      discount: "33%",
      image: "/placeholder.svg?height=200&width=300",
      badge: "جديد",
    },
    {
      title: "نتفليكس شهر",
      originalPrice: "60",
      discountPrice: "45",
      discount: "25%",
      image: "/placeholder.svg?height=200&width=300",
      badge: "خصم حصري",
    },
  ]

  return (
    <section className="py-16 bg-sky-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">العروض المميزة</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            اغتنم الفرصة واحصل على أفضل العروض والخصومات الحصرية
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {deals.map((deal, index) => (
            <div
              key={index}
              className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            >
              <div className="relative">
                <img src={deal.image || "/placeholder.svg"} alt={deal.title} className="w-full h-48 object-cover" />
                <Badge className="absolute top-3 right-3 bg-orange-500 hover:bg-orange-600">خصم {deal.discount}</Badge>
                <Badge className="absolute top-3 left-3 bg-yellow-500 hover:bg-yellow-600 text-slate-900">
                  {deal.badge}
                </Badge>
              </div>

              <div className="p-6">
                <h3 className="font-bold text-slate-900 mb-3 text-lg">{deal.title}</h3>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-green-600">{deal.discountPrice} ر.س</span>
                    <span className="text-sm text-slate-500 line-through">{deal.originalPrice} ر.س</span>
                  </div>
                </div>

                <Button className="w-full bg-slate-900 hover:bg-slate-800">اشتري الآن</Button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button
            variant="outline"
            size="lg"
            className="border-slate-900 text-slate-900 hover:bg-slate-900 hover:text-white"
          >
            عرض جميع العروض
          </Button>
        </div>
      </div>
    </section>
  )
}
